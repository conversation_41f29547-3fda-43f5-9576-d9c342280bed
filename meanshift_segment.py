import argparse, shutil, sys
from pathlib import Path
import numpy as np
from sklearn.cluster import MeanShift, estimate_bandwidth
import utils
from utils import load_nii, save_like, discover_sample_dirs, robust_norm, write_case_mapping_csv

SEED = 612385

# ------------------- Mean Shift helpers -------------------

def fit_meanshift(x: np.ndarray, bandwidth: float | None, quantile: float, sample_size: int):
    x = x.reshape(-1, 1).astype(np.float32)
    if bandwidth is None:
        bw = estimate_bandwidth(
            x,
            quantile=quantile,
            n_samples=min(sample_size, x.shape[0]),
            random_state=SEED,
        )
    else:
        bw = bandwidth
    ms = MeanShift(bandwidth=bw, bin_seeding=True)
    ms.fit(x)
    return ms.labels_, ms.cluster_centers_.reshape(-1)

def relabel_by_center(labels: np.ndarray, centers: np.ndarray, base: int) -> np.ndarray:
    uniq = np.unique(labels)
    order = np.argsort([centers[k] for k in uniq])
    mapping = {int(uniq[i]): base + 1 + i for i in order}
    return np.vectorize(lambda t: mapping[int(t)])(labels)

# ------------------- core -------------------

def two_phase_ms_labels(image: np.ndarray, mask_bool: np.ndarray,
                        bandwidth: float | None, quantile: float, sample_size: int):
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    m_flat   = mask_bool.reshape(-1)
    z = robust_norm(img_flat)
    labels_flat = np.zeros_like(img_flat, dtype=np.int32)

    # Brain
    lab_b, ctr_b = fit_meanshift(z[m_flat], bandwidth, quantile, sample_size)
    lab_b = relabel_by_center(lab_b, ctr_b, base=0)
    labels_flat[m_flat] = lab_b
    n_brain = int(lab_b.max())

    # Non-brain
    lab_nb, ctr_nb = fit_meanshift(z[~m_flat], bandwidth, quantile, sample_size)
    lab_nb = relabel_by_center(lab_nb, ctr_nb, base=n_brain)
    labels_flat[~m_flat] = lab_nb
    n_bg = int(lab_nb.max()) - n_brain

    return labels_flat.reshape(shp), n_brain, n_bg

def process_case(case_dir: Path, dst_root: Path,
                 bandwidth: float | None, quantile: float,
                 sample_size: int, mask_threshold: float, copy_inputs: bool):
    image, aff, hdr = load_nii(case_dir / "image.nii.gz")
    mask, _, _ = load_nii(case_dir / "mask.nii.gz")
    mask_bool = mask > mask_threshold

    labels, n_brain, n_bg = two_phase_ms_labels(image, mask_bool,
                                                bandwidth, quantile, sample_size)

    dst_case = dst_root / case_dir.name
    dst_case.mkdir(parents=True, exist_ok=True)
    save_like(labels, aff, hdr, dst_case / "labels.nii.gz", dtype=np.uint16)
    write_case_mapping_csv(n_brain, n_bg, dst_case / "config" / "mapping_ms.csv")

    if copy_inputs:
        shutil.copy2(case_dir / "image.nii.gz", dst_case / "image.nii.gz")
        shutil.copy2(case_dir / "mask.nii.gz", dst_case / "mask.nii.gz")

    return True, n_brain, n_bg


def main():
    ap = argparse.ArgumentParser(description="Two-phase Mean Shift over dataset (per-sample masks).")
    ap.add_argument("--src", required=True, type=Path)
    ap.add_argument("--dst", required=True, type=Path)
    ap.add_argument("--prefix", default=None)
    ap.add_argument("--copy_inputs", action="store_true")
    ap.add_argument("--mask_threshold", type=float, default=0.5)
    ap.add_argument("--ms_bandwidth", type=float, default=None)
    ap.add_argument("--ms_quantile", type=float, default=0.20)
    ap.add_argument("--ms_sample", type=int, default=5000)

    args = ap.parse_args()
    cases = discover_sample_dirs(args.src, args.prefix)
    if not cases:
        print(f"No sample folders found under {args.src} (prefix={args.prefix})", file=sys.stderr)
        sys.exit(2)

    args.dst.mkdir(parents=True, exist_ok=True)
    n_ok = total_brain = total_bg = 0

    for case in cases:
        ok, nb, ng = process_case(case, args.dst,
                                  args.ms_bandwidth, args.ms_quantile,
                                  args.ms_sample, args.mask_threshold,
                                  args.copy_inputs)
        n_ok += int(ok)
        total_brain += nb
        total_bg += ng
        print(f"[{case.name}] clusters: brain={nb}, nonbrain={ng}")

    print(f"\nProcessed {n_ok}/{len(cases)} cases.")
    if n_ok > 0:
        print(f"Average clusters → brain: {total_brain/n_ok:.2f}, nonbrain: {total_bg/n_ok:.2f}")

if __name__ == "__main__":
    main()
